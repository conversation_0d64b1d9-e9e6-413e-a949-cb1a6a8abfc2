<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Rename CAPTCHA Labeler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            color: white;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .progress-bar {
            width: 100%;
            height: 25px;
            background: #ecf0f1;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 30px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.5s ease;
            border-radius: 12px;
        }
        .image-section {
            text-align: center;
            margin-bottom: 30px;
        }
        .captcha-image {
            max-width: 500px;
            max-height: 250px;
            border: 4px solid #3498db;
            border-radius: 12px;
            margin-bottom: 20px;
            background: white;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .input-section {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        .label-input {
            padding: 15px 25px;
            font-size: 20px;
            border: 3px solid #bdc3c7;
            border-radius: 8px;
            width: 250px;
            text-align: center;
            text-transform: lowercase;
            font-weight: bold;
            transition: border-color 0.3s;
        }
        .label-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }
        .btn {
            padding: 15px 30px;
            font-size: 16px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }
        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(149, 165, 166, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }
        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
        }
        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-box {
            background: #e8f6f3;
            border-left: 5px solid #27ae60;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .info-box h4 {
            margin-top: 0;
            color: #27ae60;
        }
        .shortcuts {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .shortcuts h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        .shortcut-item {
            display: inline-block;
            margin-right: 25px;
            margin-bottom: 8px;
        }
        .key {
            background: #34495e;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
        }
        .loading {
            display: none;
            text-align: center;
            color: #7f8c8d;
            font-size: 18px;
        }
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            border-left: 5px solid #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
            border-left: 5px solid #2e7d32;
        }
        .no-images {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        .no-images h3 {
            color: #27ae60;
            font-size: 2em;
            margin-bottom: 15px;
        }
        .filename-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-family: monospace;
            color: #2c3e50;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏷️ Direct Rename CAPTCHA Labeler</h1>
            <p>Images are renamed directly with their labels - no JSON files needed!</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-item">
                <div class="stat-number" id="total-images">-</div>
                <div class="stat-label">Total Images</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="labeled-count">-</div>
                <div class="stat-label">Labeled</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="remaining-count">-</div>
                <div class="stat-label">Remaining</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="progress-percent">-</div>
                <div class="stat-label">Progress %</div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>

        <div class="info-box">
            <h4>🎯 How it works:</h4>
            <p><strong>Direct renaming:</strong> When you label an image, it's immediately renamed from <code>captcha_1.png</code> to <code>abc123.png</code> and moved to the <code>labeled_images</code> folder. No JSON files needed!</p>
        </div>

        <div class="error" id="error-message"></div>
        <div class="success" id="success-message"></div>
        <div class="loading" id="loading">Loading next image...</div>

        <div class="image-section">
            <div class="filename-display" id="filename-display" style="display: none;">
                Current file: <span id="current-filename"></span>
            </div>
            
            <img id="captcha-image" class="captcha-image" src="" alt="CAPTCHA Image" style="display: none;">
            
            <div id="no-images" class="no-images" style="display: none;">
                <h3>🎉 All images have been labeled!</h3>
                <p>Excellent work! All your CAPTCHA images have been labeled and renamed.</p>
                <p>You can now organize them for training using the button below.</p>
            </div>
        </div>

        <div class="input-section">
            <input type="text" id="label-input" class="label-input" placeholder="Enter CAPTCHA text" maxlength="10">
            <button class="btn btn-primary" onclick="submitLabel()">Label & Rename</button>
            <button class="btn btn-secondary" onclick="skipImage()">Skip</button>
        </div>

        <div class="controls">
            <button class="btn btn-success" onclick="organizeData()">Organize for Training</button>
            <button class="btn btn-secondary" onclick="refreshStats()">Refresh Stats</button>
        </div>

        <div class="shortcuts">
            <h4>⌨️ Keyboard Shortcuts</h4>
            <div class="shortcut-item"><span class="key">Enter</span> Label and rename image</div>
            <div class="shortcut-item"><span class="key">Esc</span> Skip current image</div>
            <div class="shortcut-item"><span class="key">Ctrl+R</span> Refresh statistics</div>
        </div>
    </div>

    <script>
        let currentImage = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            loadNextImage();
            setupKeyboardShortcuts();
        });

        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                if (e.target.tagName === 'INPUT') {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        submitLabel();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        skipImage();
                    }
                } else {
                    if (e.ctrlKey && e.key === 'r') {
                        e.preventDefault();
                        refreshStats();
                    }
                }
            });
        }

        async function refreshStats() {
            try {
                const response = await fetch('/api/stats');
                const stats = await response.json();
                
                document.getElementById('total-images').textContent = stats.total_images;
                document.getElementById('labeled-count').textContent = stats.labeled_count;
                document.getElementById('remaining-count').textContent = stats.unlabeled_count;
                document.getElementById('progress-percent').textContent = stats.progress_percent.toFixed(1) + '%';
                document.getElementById('progress-fill').style.width = stats.progress_percent + '%';
            } catch (error) {
                showError('Failed to load statistics');
            }
        }

        async function loadNextImage() {
            showLoading(true);
            try {
                const response = await fetch('/api/next_image');
                const data = await response.json();
                
                if (data.image_file) {
                    currentImage = data.image_file;
                    
                    document.getElementById('captcha-image').src = '/images/' + data.image_file;
                    document.getElementById('captcha-image').style.display = 'block';
                    document.getElementById('current-filename').textContent = data.image_file;
                    document.getElementById('filename-display').style.display = 'block';
                    document.getElementById('no-images').style.display = 'none';
                    
                    // Focus on input and clear it
                    document.getElementById('label-input').focus();
                    document.getElementById('label-input').value = '';
                } else {
                    document.getElementById('captcha-image').style.display = 'none';
                    document.getElementById('filename-display').style.display = 'none';
                    document.getElementById('no-images').style.display = 'block';
                }
            } catch (error) {
                showError('Failed to load next image');
            }
            showLoading(false);
        }

        async function submitLabel() {
            const label = document.getElementById('label-input').value.trim();
            if (!label) {
                showError('Please enter a label');
                return;
            }

            try {
                const response = await fetch('/api/label', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        image_file: currentImage,
                        label: label
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    showSuccess(`Image renamed to: ${label}.png`);
                    refreshStats();
                    loadNextImage();
                } else {
                    showError('Failed to rename image');
                }
            } catch (error) {
                showError('Failed to submit label');
            }
        }

        async function skipImage() {
            if (!currentImage) return;

            try {
                const response = await fetch('/api/skip', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        image_file: currentImage
                    })
                });
                
                const result = await response.json();
                if (result.success) {
                    showSuccess('Image skipped (moved to end of queue)');
                    loadNextImage();
                } else {
                    showError('Failed to skip image');
                }
            } catch (error) {
                showError('Failed to skip image');
            }
        }

        async function organizeData() {
            if (!confirm('Organize labeled images into train/val directories for training?')) {
                return;
            }

            showLoading(true);
            try {
                const response = await fetch('/api/organize');
                const result = await response.json();
                showSuccess(`Data organized! ${result.train_count} training, ${result.val_count} validation images copied to data/ directory.`);
            } catch (error) {
                showError('Failed to organize data');
            }
            showLoading(false);
        }

        function showError(message) {
            const errorEl = document.getElementById('error-message');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
            setTimeout(() => errorEl.style.display = 'none', 5000);
        }

        function showSuccess(message) {
            const successEl = document.getElementById('success-message');
            successEl.textContent = message;
            successEl.style.display = 'block';
            setTimeout(() => successEl.style.display = 'none', 4000);
        }

        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }
    </script>
</body>
</html>
