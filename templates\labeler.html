<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CAPTCHA Labeler</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .stats {
        display: flex;
        justify-content: space-around;
        margin-bottom: 30px;
        padding: 20px;
        background: #e8f4f8;
        border-radius: 8px;
      }
      .stat-item {
        text-align: center;
      }
      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #2c3e50;
      }
      .stat-label {
        color: #7f8c8d;
        margin-top: 5px;
      }
      .image-section {
        text-align: center;
        margin-bottom: 30px;
      }
      .captcha-image {
        max-width: 400px;
        max-height: 200px;
        border: 3px solid #3498db;
        border-radius: 8px;
        margin-bottom: 20px;
        background: white;
        padding: 10px;
      }
      .input-section {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
      }
      .label-input {
        padding: 12px 20px;
        font-size: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        width: 200px;
        text-align: center;
        text-transform: lowercase;
      }
      .label-input:focus {
        outline: none;
        border-color: #3498db;
      }
      .btn {
        padding: 12px 24px;
        font-size: 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.3s;
      }
      .btn-primary {
        background-color: #3498db;
        color: white;
      }
      .btn-primary:hover {
        background-color: #2980b9;
      }
      .btn-secondary {
        background-color: #95a5a6;
        color: white;
      }
      .btn-secondary:hover {
        background-color: #7f8c8d;
      }
      .btn-success {
        background-color: #27ae60;
        color: white;
      }
      .btn-success:hover {
        background-color: #229954;
      }
      .btn-warning {
        background-color: #f39c12;
        color: white;
      }
      .btn-warning:hover {
        background-color: #e67e22;
      }
      .controls {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 30px;
      }

      .progress-bar {
        width: 100%;
        height: 20px;
        background: #ecf0f1;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
      }
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #3498db, #2ecc71);
        transition: width 0.3s ease;
      }
      .shortcuts {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        margin-top: 20px;
      }
      .shortcuts h4 {
        margin-top: 0;
        color: #2c3e50;
      }
      .shortcut-item {
        display: inline-block;
        margin-right: 20px;
        margin-bottom: 5px;
      }
      .key {
        background: #34495e;
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-family: monospace;
      }
      .loading {
        display: none;
        text-align: center;
        color: #7f8c8d;
      }
      .error {
        background: #ffebee;
        color: #c62828;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 15px;
        display: none;
      }
      .success {
        background: #e8f5e8;
        color: #2e7d32;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 15px;
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🔤 CAPTCHA Labeler</h1>
        <p>Efficiently label your CAPTCHA images for training</p>
      </div>

      <div class="stats" id="stats">
        <div class="stat-item">
          <div class="stat-number" id="total-images">-</div>
          <div class="stat-label">Total Images</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="labeled-count">-</div>
          <div class="stat-label">Labeled</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="remaining-count">-</div>
          <div class="stat-label">Remaining</div>
        </div>
        <div class="stat-item">
          <div class="stat-number" id="progress-percent">-</div>
          <div class="stat-label">Progress %</div>
        </div>
      </div>

      <div class="progress-bar">
        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
      </div>

      <div class="error" id="error-message"></div>
      <div class="success" id="success-message"></div>
      <div class="loading" id="loading">Loading...</div>

      <div class="image-section">
        <img
          id="captcha-image"
          class="captcha-image"
          src=""
          alt="CAPTCHA Image"
          style="display: none"
        />
        <div id="no-images" style="display: none">
          <h3>🎉 All images have been labeled!</h3>
          <p>Great job! You can now organize your data for training.</p>
        </div>
      </div>

      <div class="input-section">
        <input
          type="text"
          id="label-input"
          class="label-input"
          placeholder="Enter CAPTCHA text"
          maxlength="8"
        />
        <button class="btn btn-primary" onclick="submitLabel()">Submit</button>
      </div>

      <div class="shortcuts">
        <h4>⌨️ Keyboard Shortcuts</h4>
        <div class="shortcut-item">
          <span class="key">Enter</span> Submit label
        </div>
      </div>
    </div>

    <script>
      let currentImage = null;

      // Initialize
      document.addEventListener("DOMContentLoaded", function () {
        refreshStats();
        loadNextImage();
        setupKeyboardShortcuts();
      });

      function setupKeyboardShortcuts() {
        document.addEventListener("keydown", function (e) {
          if (e.target.tagName === "INPUT") {
            if (e.key === "Enter") {
              e.preventDefault();
              submitLabel();
            }
          }
        });
      }

      async function refreshStats() {
        try {
          const response = await fetch("/api/stats");
          const stats = await response.json();

          document.getElementById("total-images").textContent =
            stats.total_images;
          document.getElementById("labeled-count").textContent =
            stats.labeled_count;
          document.getElementById("remaining-count").textContent =
            stats.unlabeled_count;
          document.getElementById("progress-percent").textContent =
            stats.progress_percent.toFixed(1) + "%";
          document.getElementById("progress-fill").style.width =
            stats.progress_percent + "%";
        } catch (error) {
          showError("Failed to load statistics");
        }
      }

      async function loadNextImage() {
        showLoading(true);
        try {
          const response = await fetch("/api/next_image");
          const data = await response.json();

          if (data.image_file) {
            currentImage = data.image_file;
            document.getElementById("captcha-image").src =
              "/images/" + data.image_file;
            document.getElementById("captcha-image").style.display = "block";
            document.getElementById("no-images").style.display = "none";

            // Focus on input
            document.getElementById("label-input").focus();
            document.getElementById("label-input").value = "";
          } else {
            document.getElementById("captcha-image").style.display = "none";
            document.getElementById("no-images").style.display = "block";
          }
        } catch (error) {
          showError("Failed to load next image");
        }
        showLoading(false);
      }

      async function submitLabel() {
        const label = document.getElementById("label-input").value.trim();
        if (!label) {
          showError("Please enter a label");
          return;
        }

        try {
          const response = await fetch("/api/label", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              image_file: currentImage,
              label: label,
            }),
          });

          const result = await response.json();
          if (result.success) {
            showSuccess("Label saved successfully!");
            refreshStats();
            loadNextImage();
          } else {
            showError("Failed to save label");
          }
        } catch (error) {
          showError("Failed to submit label");
        }
      }

      function showError(message) {
        const errorEl = document.getElementById("error-message");
        errorEl.textContent = message;
        errorEl.style.display = "block";
        setTimeout(() => (errorEl.style.display = "none"), 5000);
      }

      function showSuccess(message) {
        const successEl = document.getElementById("success-message");
        successEl.textContent = message;
        successEl.style.display = "block";
        setTimeout(() => (successEl.style.display = "none"), 3000);
      }

      function showLoading(show) {
        document.getElementById("loading").style.display = show
          ? "block"
          : "none";
      }
    </script>
  </body>
</html>
