#!/usr/bin/env python3
"""
Demo script to test the CAPTCHA labeling tools
"""

import os
import sys
from pathlib import Path
import subprocess

def check_setup():
    """Check if the labeling tools are properly set up"""
    print("🔍 Checking CAPTCHA Labeling Tools Setup")
    print("=" * 50)
    
    # Check if required files exist
    required_files = [
        "captcha_labeler.py",
        "batch_label.py", 
        "templates/labeler.html",
        "requirements_labeling.txt"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            print(f"✅ {file}")
    
    if missing_files:
        print(f"\n❌ Missing files: {', '.join(missing_files)}")
        return False
    
    # Check image directory
    image_dir = Path("captcha_images")
    if not image_dir.exists():
        print(f"\n❌ Image directory not found: {image_dir}")
        return False
    
    # Count images
    extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    image_count = 0
    for ext in extensions:
        image_count += len(list(image_dir.glob(f"*{ext}")))
        image_count += len(list(image_dir.glob(f"*{ext.upper()}")))
    
    print(f"✅ Found {image_count} images in {image_dir}")
    
    if image_count == 0:
        print("⚠️  No images found - please add CAPTCHA images to the captcha_images directory")
        return False
    
    return True

def run_demo():
    """Run a quick demo of the labeling tools"""
    if not check_setup():
        print("\n❌ Setup check failed. Please run setup_labeling.py first.")
        return False
    
    print("\n🎯 CAPTCHA Labeling Tools Demo")
    print("=" * 50)
    
    while True:
        print("\nChoose a demo option:")
        print("1. 📊 Show statistics")
        print("2. 🤖 Auto-label 10 images (OCR demo)")
        print("3. 🌐 Launch web interface")
        print("4. 📁 Show current progress")
        print("5. ❌ Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            show_statistics()
        elif choice == "2":
            auto_label_demo()
        elif choice == "3":
            launch_web_interface()
        elif choice == "4":
            show_progress()
        elif choice == "5":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-5.")

def show_statistics():
    """Show image statistics"""
    print("\n📊 Image Statistics")
    print("-" * 30)
    
    try:
        result = subprocess.run([
            sys.executable, "captcha_labeler.py", "--mode", "stats"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ Error: {result.stderr}")
    except Exception as e:
        print(f"❌ Failed to get statistics: {e}")

def auto_label_demo():
    """Demo auto-labeling with OCR"""
    print("\n🤖 Auto-labeling Demo (10 images)")
    print("-" * 40)
    print("This will use OCR to automatically suggest labels for 10 images...")
    
    confirm = input("Continue? (y/n): ").lower().strip()
    if confirm != 'y':
        return
    
    try:
        result = subprocess.run([
            sys.executable, "captcha_labeler.py", 
            "--mode", "auto", "--count", "10"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Auto-labeling completed!")
            print(result.stdout)
        else:
            print(f"❌ Error: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ Auto-labeling timed out (this is normal for the first run)")
    except Exception as e:
        print(f"❌ Failed to auto-label: {e}")

def launch_web_interface():
    """Launch the web interface"""
    print("\n🌐 Launching Web Interface")
    print("-" * 30)
    print("The web interface will open at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    print()
    
    confirm = input("Launch web interface? (y/n): ").lower().strip()
    if confirm != 'y':
        return
    
    try:
        subprocess.run([
            sys.executable, "captcha_labeler.py", "--mode", "web"
        ])
    except KeyboardInterrupt:
        print("\n👋 Web interface stopped")
    except Exception as e:
        print(f"❌ Failed to launch web interface: {e}")

def show_progress():
    """Show current labeling progress"""
    print("\n📁 Current Progress")
    print("-" * 20)
    
    # Check for existing progress files
    progress_file = Path("labeled_data/progress.json")
    labels_file = Path("labeled_data/labels.json")
    
    if progress_file.exists():
        try:
            import json
            with open(progress_file, 'r') as f:
                progress = json.load(f)
            print(f"Total labeled: {progress.get('total_labeled', 0)}")
        except:
            print("❌ Could not read progress file")
    else:
        print("📝 No progress file found - labeling not started yet")
    
    if labels_file.exists():
        try:
            import json
            with open(labels_file, 'r') as f:
                labels = json.load(f)
            print(f"Labels in database: {len(labels)}")
            
            # Show a few examples
            if labels:
                print("\nSample labels:")
                for i, (filename, label) in enumerate(list(labels.items())[:5]):
                    print(f"  {filename} -> {label}")
                if len(labels) > 5:
                    print(f"  ... and {len(labels) - 5} more")
        except:
            print("❌ Could not read labels file")
    else:
        print("📝 No labels file found")
    
    # Check organized data
    train_dir = Path("labeled_data/train")
    val_dir = Path("labeled_data/val")
    
    if train_dir.exists():
        train_count = len(list(train_dir.glob("*.png"))) + len(list(train_dir.glob("*.jpg")))
        print(f"Training images: {train_count}")
    
    if val_dir.exists():
        val_count = len(list(val_dir.glob("*.png"))) + len(list(val_dir.glob("*.jpg")))
        print(f"Validation images: {val_count}")

def main():
    print("🔤 CAPTCHA Labeling Tools - Demo")
    print("=" * 50)
    print("This demo will help you test the labeling tools")
    print()
    
    run_demo()

if __name__ == "__main__":
    main()
