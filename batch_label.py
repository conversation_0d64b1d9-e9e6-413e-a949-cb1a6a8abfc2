#!/usr/bin/env python3
"""
Batch CAPTCHA Labeling Script
Quick command-line tool for batch operations on CAPTCHA images
"""

import os
import sys
import json
import shutil
import random
from pathlib import Path
from PIL import Image
import cv2
import numpy as np
import pytesseract
from typing import List, Dict

def preprocess_for_ocr(image_path: str) -> np.ndarray:
    """Enhanced preprocessing for CAPTCHA OCR"""
    img = cv2.imread(image_path)
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Multiple preprocessing approaches
    approaches = []
    
    # Approach 1: Standard threshold
    _, thresh1 = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    approaches.append(thresh1)
    
    # Approach 2: Adaptive threshold
    thresh2 = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
    approaches.append(thresh2)
    
    # Approach 3: Morphological operations
    kernel = np.ones((2, 2), np.uint8)
    morph = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
    _, thresh3 = cv2.threshold(morph, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    approaches.append(thresh3)
    
    return approaches

def ocr_with_multiple_configs(image_path: str) -> List[str]:
    """Try multiple OCR configurations and return all results"""
    approaches = preprocess_for_ocr(image_path)
    
    # Different OCR configurations
    configs = [
        '--psm 8 -c tessedit_char_whitelist=abcdefghijklmnopqrstuvwxyz0123456789',
        '--psm 7 -c tessedit_char_whitelist=abcdefghijklmnopqrstuvwxyz0123456789',
        '--psm 6 -c tessedit_char_whitelist=abcdefghijklmnopqrstuvwxyz0123456789',
        '--psm 13 -c tessedit_char_whitelist=abcdefghijklmnopqrstuvwxyz0123456789'
    ]
    
    results = []
    
    for approach in approaches:
        for config in configs:
            try:
                # Invert if needed
                if np.mean(approach) < 127:
                    approach = cv2.bitwise_not(approach)
                
                text = pytesseract.image_to_string(approach, config=config).strip().lower()
                text = ''.join(c for c in text if c.isalnum())
                
                if text and 2 <= len(text) <= 8:
                    results.append(text)
            except:
                continue
    
    return list(set(results))  # Remove duplicates

def auto_label_images(image_dir: str, output_file: str = "auto_labels.json", 
                     start_idx: int = 0, count: int = None):
    """Auto-label images using OCR with multiple approaches"""
    image_dir = Path(image_dir)
    
    # Get image files
    extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    image_files = []
    for ext in extensions:
        image_files.extend(image_dir.glob(f"*{ext}"))
        image_files.extend(image_dir.glob(f"*{ext.upper()}"))
    
    image_files = sorted(image_files)
    
    if count is None:
        count = len(image_files) - start_idx
    
    end_idx = min(start_idx + count, len(image_files))
    
    print(f"Processing {end_idx - start_idx} images from index {start_idx}")
    
    results = {}
    successful = 0
    
    for i in range(start_idx, end_idx):
        image_file = image_files[i]
        print(f"Processing {i+1}/{end_idx}: {image_file.name}", end=" ... ")
        
        try:
            predictions = ocr_with_multiple_configs(str(image_file))
            
            if predictions:
                # Use the most common prediction or the first one
                best_prediction = max(set(predictions), key=predictions.count)
                results[image_file.name] = {
                    "label": best_prediction,
                    "alternatives": predictions,
                    "confidence": predictions.count(best_prediction) / len(predictions)
                }
                print(f"✓ {best_prediction} (confidence: {results[image_file.name]['confidence']:.2f})")
                successful += 1
            else:
                results[image_file.name] = {"label": "", "alternatives": [], "confidence": 0}
                print("✗ No prediction")
                
        except Exception as e:
            print(f"✗ Error: {e}")
            results[image_file.name] = {"label": "", "alternatives": [], "confidence": 0}
    
    # Save results
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nCompleted! {successful}/{end_idx - start_idx} images labeled successfully")
    print(f"Results saved to {output_file}")
    
    return results

def review_predictions(predictions_file: str):
    """Interactive review of OCR predictions"""
    with open(predictions_file, 'r') as f:
        predictions = json.load(f)
    
    print("=== PREDICTION REVIEW ===")
    print("Commands: [a]ccept, [e]dit, [s]kip, [q]uit")
    print()
    
    accepted_labels = {}
    
    for filename, data in predictions.items():
        if not data['label']:
            continue
            
        print(f"File: {filename}")
        print(f"Prediction: {data['label']} (confidence: {data['confidence']:.2f})")
        if data['alternatives']:
            print(f"Alternatives: {', '.join(data['alternatives'])}")
        
        while True:
            choice = input("Action [a/e/s/q]: ").lower().strip()
            
            if choice == 'a':
                accepted_labels[filename] = data['label']
                break
            elif choice == 'e':
                new_label = input("Enter correct label: ").strip().lower()
                if new_label:
                    accepted_labels[filename] = new_label
                break
            elif choice == 's':
                break
            elif choice == 'q':
                print(f"Saving {len(accepted_labels)} accepted labels...")
                return accepted_labels
            else:
                print("Invalid choice. Use a/e/s/q")
        
        print()
    
    return accepted_labels

def organize_dataset(labels_dict: Dict[str, str], image_dir: str, 
                    output_dir: str = "dataset", train_split: float = 0.8):
    """Organize labeled images into train/val directories"""
    image_dir = Path(image_dir)
    output_dir = Path(output_dir)
    
    # Create directories
    train_dir = output_dir / "train"
    val_dir = output_dir / "val"
    
    train_dir.mkdir(parents=True, exist_ok=True)
    val_dir.mkdir(parents=True, exist_ok=True)
    
    # Clear existing files
    for f in train_dir.glob("*"):
        f.unlink()
    for f in val_dir.glob("*"):
        f.unlink()
    
    # Split data
    labeled_files = list(labels_dict.keys())
    random.shuffle(labeled_files)
    
    split_idx = int(len(labeled_files) * train_split)
    train_files = labeled_files[:split_idx]
    val_files = labeled_files[split_idx:]
    
    # Copy files with proper naming
    def copy_files(files, target_dir):
        for filename in files:
            label = labels_dict[filename]
            src_path = image_dir / filename
            
            if src_path.exists():
                # Create unique filename: label_originalname
                dst_name = f"{label}_{filename}"
                dst_path = target_dir / dst_name
                shutil.copy2(src_path, dst_path)
    
    copy_files(train_files, train_dir)
    copy_files(val_files, val_dir)
    
    print(f"Dataset organized:")
    print(f"  Training: {len(train_files)} images in {train_dir}")
    print(f"  Validation: {len(val_files)} images in {val_dir}")
    
    return len(train_files), len(val_files)

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python batch_label.py auto <image_dir> [start_idx] [count]")
        print("  python batch_label.py review <predictions_file>")
        print("  python batch_label.py organize <labels_file> <image_dir>")
        print("  python batch_label.py stats <image_dir>")
        return
    
    command = sys.argv[1]
    
    if command == "auto":
        if len(sys.argv) < 3:
            print("Usage: python batch_label.py auto <image_dir> [start_idx] [count]")
            return
        
        image_dir = sys.argv[2]
        start_idx = int(sys.argv[3]) if len(sys.argv) > 3 else 0
        count = int(sys.argv[4]) if len(sys.argv) > 4 else None
        
        auto_label_images(image_dir, start_idx=start_idx, count=count)
    
    elif command == "review":
        if len(sys.argv) < 3:
            print("Usage: python batch_label.py review <predictions_file>")
            return
        
        predictions_file = sys.argv[2]
        labels = review_predictions(predictions_file)
        
        # Save reviewed labels
        output_file = "reviewed_labels.json"
        with open(output_file, 'w') as f:
            json.dump(labels, f, indent=2)
        print(f"Reviewed labels saved to {output_file}")
    
    elif command == "organize":
        if len(sys.argv) < 4:
            print("Usage: python batch_label.py organize <labels_file> <image_dir>")
            return
        
        labels_file = sys.argv[2]
        image_dir = sys.argv[3]
        
        with open(labels_file, 'r') as f:
            labels = json.load(f)
        
        organize_dataset(labels, image_dir)
    
    elif command == "stats":
        if len(sys.argv) < 3:
            print("Usage: python batch_label.py stats <image_dir>")
            return
        
        image_dir = Path(sys.argv[2])
        extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        
        total_files = 0
        for ext in extensions:
            total_files += len(list(image_dir.glob(f"*{ext}")))
            total_files += len(list(image_dir.glob(f"*{ext.upper()}")))
        
        print(f"Total images in {image_dir}: {total_files}")
    
    else:
        print(f"Unknown command: {command}")
        print("Available commands: auto, review, organize, stats")

if __name__ == "__main__":
    main()
