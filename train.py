# train.py

import os
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image, ImageOps, ImageFilter

# ----------------------------
# 1. Define character set globally
# ----------------------------
CHARS = "abcdefghijklmnopqrstuvwxyz0123456789"
NUM_CLASSES = len(CHARS)
SEQ_LEN = 6  # captchas always have 6 characters

char_to_idx = {c: i for i, c in enumerate(CHARS)}
idx_to_char = {i: c for i, c in enumerate(CHARS)}


# ----------------------------
# 2. Dataset class
# ----------------------------
class CaptchaDataset(Dataset):
    def __init__(self, root, transform=None):
        self.root = root
        # only keep image files
        self.files = [f for f in os.listdir(root) if f.lower().endswith((".jpg", ".png", ".jpeg"))]
        self.transform = transform

    def __len__(self):
        return len(self.files)

    def __getitem__(self, idx):
        file = self.files[idx]
        path = os.path.join(self.root, file)

        # load image
        image = Image.open(path).convert("RGB")

        # preprocessing: grayscale + contrast + slight blur (helps with noisy lines)
        image = ImageOps.grayscale(image)
        image = ImageOps.autocontrast(image)
        image = image.filter(ImageFilter.MedianFilter(size=3))

        if self.transform:
            image = self.transform(image)

        # extract label (remove .jpg/.png extension)
        label_str = os.path.splitext(file)[0]

        # sanity check: must be exactly SEQ_LEN chars
        if len(label_str) != SEQ_LEN:
            raise ValueError(f"Invalid label '{label_str}' in file {file}. Expected {SEQ_LEN} chars.")

        label = [char_to_idx[c] for c in label_str]

        return image, torch.tensor(label)


# ----------------------------
# 3. Data transforms
# ----------------------------
transform = transforms.Compose([
    transforms.Resize((64, 200)),  # resize all images to same size
    transforms.ToTensor()
])


# ----------------------------
# 4. Data loaders
# ----------------------------
train_ds = CaptchaDataset("data/train", transform)
val_ds = CaptchaDataset("data/val", transform)

train_dl = DataLoader(train_ds, batch_size=8, shuffle=True)
val_dl = DataLoader(val_ds, batch_size=8)


# ----------------------------
# 5. Model definition
# ----------------------------
class CaptchaModel(nn.Module):
    def __init__(self, num_classes=NUM_CLASSES, seq_len=SEQ_LEN):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(1, 32, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
            nn.Conv2d(32, 64, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
            nn.Conv2d(64, 128, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
        )
        self.fc = nn.Linear(128 * 8 * 25, seq_len * num_classes)
        self.num_classes = num_classes
        self.seq_len = seq_len

    def forward(self, x):
        x = self.conv(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        x = x.view(-1, self.seq_len, self.num_classes)
        return x


# ----------------------------
# 6. Training setup
# ----------------------------
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

model = CaptchaModel().to(device)

criterion = nn.CrossEntropyLoss()
optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)


# ----------------------------
# 7. Training loop
# ----------------------------
EPOCHS = 20
for epoch in range(EPOCHS):
    model.train()
    total_loss = 0
    for imgs, labels in train_dl:
        imgs, labels = imgs.to(device), labels.to(device)

        outputs = model(imgs)  # [batch, 6, num_classes]
        loss = 0
        for i in range(SEQ_LEN):
            loss += criterion(outputs[:, i, :], labels[:, i])

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        total_loss += loss.item()

    # validation
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for imgs, labels in val_dl:
            imgs, labels = imgs.to(device), labels.to(device)
            outputs = model(imgs)
            preds = outputs.argmax(dim=2)  # [batch, 6]

            for i in range(len(labels)):
                if torch.equal(preds[i], labels[i]):
                    correct += 1
                total += 1

    print(f"Epoch {epoch+1}/{EPOCHS} - Loss: {total_loss/len(train_dl):.4f} "
          f"- Val Acc: {correct}/{total} = {100*correct/total:.2f}%")


# ----------------------------
# 8. Save model
# ----------------------------
torch.save(model.state_dict(), "captcha_model.pth")
print("✅ Training complete. Model saved as captcha_model.pth")
