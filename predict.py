# predict.py

import torch
from PIL import Image, ImageOps, ImageFilter
from torchvision import transforms
import torch.nn as nn
import os

# ----------------------------
# 1. Character set (same as training)
# ----------------------------
CHARS = "abcdefghijklmnopqrstuvwxyz0123456789"
NUM_CLASSES = len(CHARS)
PAD_IDX = NUM_CLASSES  # extra class for padding
MAX_LEN = 6

char_to_idx = {c: i for i, c in enumerate(CHARS)}
idx_to_char = {i: c for i, c in enumerate(CHARS)}

# ----------------------------
# 2. Model definition (same as training)
# ----------------------------
class CaptchaModel(nn.Module):
    def __init__(self, num_classes=NUM_CLASSES+1, max_len=MAX_LEN):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(1, 32, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
            nn.Conv2d(32, 64, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
            nn.Conv2d(64, 128, 3, padding=1), nn.ReLU(), nn.MaxPool2d(2),
        )
        self.fc = nn.Linear(128*8*25, max_len*num_classes)
        self.num_classes = num_classes
        self.max_len = max_len

    def forward(self, x):
        x = self.conv(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)
        x = x.view(-1, self.max_len, self.num_classes)
        return x

# ----------------------------
# 3. Load trained model
# ----------------------------
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

model = CaptchaModel().to(device)
model.load_state_dict(torch.load("captcha_model.pth", map_location=device))
model.eval()

# ----------------------------
# 4. Image preprocessing
# ----------------------------
def preprocess_image(image_path):
    img = Image.open(image_path).convert("RGB")
    img = ImageOps.grayscale(img)
    img = ImageOps.autocontrast(img)
    img = img.filter(ImageFilter.MedianFilter(size=3))

    transform = transforms.Compose([
        transforms.Resize((64, 200)),
        transforms.ToTensor()
    ])
    img = transform(img)
    return img.unsqueeze(0)  # add batch dimension

# ----------------------------
# 5. Predict function
# ----------------------------
def predict_captcha(image_path):
    img = preprocess_image(image_path).to(device)
    with torch.no_grad():
        output = model(img)  # [1, max_len, num_classes]
        pred_idx = output.argmax(dim=2).squeeze(0).tolist()
        # remove padding index
        pred_text = "".join([idx_to_char[i] for i in pred_idx if i != PAD_IDX])
    return pred_text

# ----------------------------
# 6. Example usage
# ----------------------------
if __name__ == "__main__":
    test_image = "data/test/test1.jpg"  # path to your test CAPTCHA
    if not os.path.exists(test_image):
        print(f"❌ Test image not found: {test_image}")
    else:
        result = predict_captcha(test_image)
        print(f"Predicted CAPTCHA: {result}")
