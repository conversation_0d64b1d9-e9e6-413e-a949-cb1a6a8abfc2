#!/usr/bin/env python3
"""
CAPTCHA Labeling Tool
A comprehensive tool for labeling CAPTCHA images with multiple approaches:
1. Web-based interface for manual labeling
2. OCR pre-labeling for speed
3. Batch processing and validation
"""

import os
import shutil
import json
import random
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_from_directory
import cv2
import numpy as np
from PIL import Image
import pytesseract
import argparse
from typing import List, Dict, Tuple

class CaptchaLabeler:
    def __init__(self, image_dir: str = "captcha_images", output_dir: str = "labeled_data"):
        self.image_dir = Path(image_dir)
        self.output_dir = Path(output_dir)
        self.labels_file = self.output_dir / "labels.json"
        self.progress_file = self.output_dir / "progress.json"
        
        # Create output directories
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / "train").mkdir(exist_ok=True)
        (self.output_dir / "val").mkdir(exist_ok=True)
        (self.output_dir / "unlabeled").mkdir(exist_ok=True)
        
        # Load existing labels and progress
        self.labels = self.load_labels()
        self.progress = self.load_progress()
        
        # Get all image files
        self.image_files = self.get_image_files()
        
    def get_image_files(self) -> List[str]:
        """Get all image files from the input directory"""
        extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        files = []
        for ext in extensions:
            files.extend(self.image_dir.glob(f"*{ext}"))
            files.extend(self.image_dir.glob(f"*{ext.upper()}"))
        return [f.name for f in sorted(files)]
    
    def load_labels(self) -> Dict:
        """Load existing labels from JSON file"""
        if self.labels_file.exists():
            with open(self.labels_file, 'r') as f:
                return json.load(f)
        return {}
    
    def save_labels(self):
        """Save labels to JSON file"""
        with open(self.labels_file, 'w') as f:
            json.dump(self.labels, f, indent=2)
    
    def load_progress(self) -> Dict:
        """Load progress information"""
        if self.progress_file.exists():
            with open(self.progress_file, 'r') as f:
                return json.load(f)
        return {"current_index": 0, "total_labeled": 0}
    
    def save_progress(self):
        """Save progress information"""
        with open(self.progress_file, 'w') as f:
            json.dump(self.progress, f, indent=2)
    
    def preprocess_image_for_ocr(self, image_path: str) -> np.ndarray:
        """Preprocess image for better OCR results"""
        # Read image
        img = cv2.imread(str(self.image_dir / image_path))
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # Apply threshold to get binary image
        _, thresh = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphological operations to clean up
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        # Invert if background is dark
        if np.mean(cleaned) < 127:
            cleaned = cv2.bitwise_not(cleaned)
            
        return cleaned
    
    def ocr_predict_label(self, image_path: str) -> str:
        """Use OCR to predict CAPTCHA label"""
        try:
            # Preprocess image
            processed_img = self.preprocess_image_for_ocr(image_path)
            
            # OCR configuration for CAPTCHA-like text
            config = '--psm 8 -c tessedit_char_whitelist=abcdefghijklmnopqrstuvwxyz0123456789'
            
            # Extract text
            text = pytesseract.image_to_string(processed_img, config=config).strip().lower()
            
            # Clean up the result
            text = ''.join(c for c in text if c.isalnum())
            
            return text if len(text) <= 8 else text[:8]  # Limit length
            
        except Exception as e:
            print(f"OCR failed for {image_path}: {e}")
            return ""
    
    def auto_label_batch(self, start_idx: int = 0, count: int = 100) -> Dict:
        """Auto-label a batch of images using OCR"""
        results = {"success": 0, "failed": 0, "predictions": {}}
        
        end_idx = min(start_idx + count, len(self.image_files))
        
        for i in range(start_idx, end_idx):
            image_file = self.image_files[i]
            
            if image_file in self.labels:
                continue  # Skip already labeled
                
            predicted_label = self.ocr_predict_label(image_file)
            
            if predicted_label:
                results["predictions"][image_file] = predicted_label
                results["success"] += 1
            else:
                results["failed"] += 1
        
        return results
    
    def get_next_unlabeled_image(self) -> Tuple[str, int]:
        """Get the next unlabeled image"""
        for i, image_file in enumerate(self.image_files):
            if image_file not in self.labels:
                return image_file, i
        return None, -1
    
    def label_image(self, image_file: str, label: str) -> bool:
        """Label a single image"""
        if not label or not label.strip():
            return False
            
        # Clean the label
        clean_label = ''.join(c for c in label.lower().strip() if c.isalnum())
        
        if not clean_label:
            return False
            
        self.labels[image_file] = clean_label
        self.progress["total_labeled"] = len(self.labels)
        
        self.save_labels()
        self.save_progress()
        
        return True
    
    def organize_labeled_data(self, train_split: float = 0.8):
        """Organize labeled images into train/val directories with proper naming"""
        labeled_files = list(self.labels.keys())
        random.shuffle(labeled_files)
        
        train_count = int(len(labeled_files) * train_split)
        train_files = labeled_files[:train_count]
        val_files = labeled_files[train_count:]
        
        # Clear existing organized data
        for subdir in ["train", "val"]:
            subdir_path = self.output_dir / subdir
            if subdir_path.exists():
                shutil.rmtree(subdir_path)
            subdir_path.mkdir()
        
        # Copy files with proper naming
        for files, subdir in [(train_files, "train"), (val_files, "val")]:
            for image_file in files:
                label = self.labels[image_file]
                src_path = self.image_dir / image_file
                
                # Create unique filename with label
                base_name = f"{label}_{image_file}"
                dst_path = self.output_dir / subdir / base_name
                
                shutil.copy2(src_path, dst_path)
        
        print(f"Organized {len(train_files)} training and {len(val_files)} validation images")
        return len(train_files), len(val_files)
    
    def get_stats(self) -> Dict:
        """Get labeling statistics"""
        total_images = len(self.image_files)
        labeled_count = len(self.labels)
        
        return {
            "total_images": total_images,
            "labeled_count": labeled_count,
            "unlabeled_count": total_images - labeled_count,
            "progress_percent": (labeled_count / total_images * 100) if total_images > 0 else 0
        }

# Flask Web Interface
app = Flask(__name__)
labeler = None

@app.route('/')
def index():
    return render_template('labeler.html')

@app.route('/api/stats')
def get_stats():
    return jsonify(labeler.get_stats())

@app.route('/api/next_image')
def get_next_image():
    image_file, index = labeler.get_next_unlabeled_image()
    if image_file:
        # Try to get OCR prediction
        ocr_prediction = labeler.ocr_predict_label(image_file)
        return jsonify({
            "image_file": image_file,
            "index": index,
            "ocr_prediction": ocr_prediction
        })
    return jsonify({"image_file": None})

@app.route('/api/label', methods=['POST'])
def label_image():
    data = request.json
    image_file = data.get('image_file')
    label = data.get('label')
    
    success = labeler.label_image(image_file, label)
    return jsonify({"success": success})

@app.route('/api/auto_label', methods=['POST'])
def auto_label():
    data = request.json
    start_idx = data.get('start_idx', 0)
    count = data.get('count', 100)
    
    results = labeler.auto_label_batch(start_idx, count)
    return jsonify(results)

@app.route('/api/organize')
def organize_data():
    train_count, val_count = labeler.organize_labeled_data()
    return jsonify({"train_count": train_count, "val_count": val_count})

@app.route('/images/<filename>')
def serve_image(filename):
    return send_from_directory(labeler.image_dir, filename)

def main():
    global labeler
    
    parser = argparse.ArgumentParser(description='CAPTCHA Labeling Tool')
    parser.add_argument('--mode', choices=['web', 'auto', 'organize', 'stats'], 
                       default='web', help='Operation mode')
    parser.add_argument('--image-dir', default='captcha_images', 
                       help='Directory containing CAPTCHA images')
    parser.add_argument('--output-dir', default='labeled_data', 
                       help='Output directory for labeled data')
    parser.add_argument('--count', type=int, default=100, 
                       help='Number of images to auto-label')
    parser.add_argument('--port', type=int, default=5000, 
                       help='Port for web interface')
    
    args = parser.parse_args()
    
    labeler = CaptchaLabeler(args.image_dir, args.output_dir)
    
    if args.mode == 'web':
        print(f"Starting web interface at http://localhost:{args.port}")
        print("Press Ctrl+C to stop")
        app.run(debug=True, port=args.port, host='0.0.0.0')
        
    elif args.mode == 'auto':
        print(f"Auto-labeling {args.count} images...")
        results = labeler.auto_label_batch(0, args.count)
        print(f"Success: {results['success']}, Failed: {results['failed']}")
        
        # Show some predictions for review
        for img, pred in list(results['predictions'].items())[:10]:
            print(f"{img} -> {pred}")
            
    elif args.mode == 'organize':
        print("Organizing labeled data...")
        train_count, val_count = labeler.organize_labeled_data()
        print(f"Organized: {train_count} train, {val_count} validation")
        
    elif args.mode == 'stats':
        stats = labeler.get_stats()
        print(f"Total images: {stats['total_images']}")
        print(f"Labeled: {stats['labeled_count']}")
        print(f"Progress: {stats['progress_percent']:.1f}%")

if __name__ == '__main__':
    main()
