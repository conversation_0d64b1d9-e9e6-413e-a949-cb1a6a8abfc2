#!/usr/bin/env python3
"""
Setup script for CAPTCHA labeling tools
Installs dependencies and checks system requirements
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True)
        print(f"✅ Tesseract OCR found: {result.stdout.split()[1]}")
        return True
    except FileNotFoundError:
        print("❌ Tesseract OCR not found")
        return False

def install_tesseract():
    """Install Tesseract OCR based on the operating system"""
    system = platform.system().lower()
    
    if system == "windows":
        print("📋 For Windows, please install Tesseract manually:")
        print("   1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   2. Install the executable")
        print("   3. Add to PATH or set TESSDATA_PREFIX environment variable")
        return False
    
    elif system == "darwin":  # macOS
        return run_command("brew install tesseract", "Installing Tesseract via Homebrew")
    
    elif system == "linux":
        # Try different package managers
        if run_command("which apt-get", "Checking for apt-get"):
            return run_command("sudo apt-get update && sudo apt-get install -y tesseract-ocr", 
                             "Installing Tesseract via apt-get")
        elif run_command("which yum", "Checking for yum"):
            return run_command("sudo yum install -y tesseract", 
                             "Installing Tesseract via yum")
        elif run_command("which pacman", "Checking for pacman"):
            return run_command("sudo pacman -S tesseract", 
                             "Installing Tesseract via pacman")
    
    print(f"❌ Automatic installation not supported for {system}")
    print("   Please install Tesseract OCR manually")
    return False

def install_python_dependencies():
    """Install Python dependencies"""
    requirements_file = Path("requirements_labeling.txt")
    
    if not requirements_file.exists():
        print("❌ requirements_labeling.txt not found")
        return False
    
    return run_command(f"{sys.executable} -m pip install -r {requirements_file}", 
                      "Installing Python dependencies")

def create_directories():
    """Create necessary directories"""
    directories = [
        "labeled_data",
        "labeled_data/train", 
        "labeled_data/val",
        "labeled_data/unlabeled",
        "templates"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def check_image_directory():
    """Check if captcha_images directory exists and has images"""
    image_dir = Path("captcha_images")
    
    if not image_dir.exists():
        print("❌ captcha_images directory not found")
        print("   Please ensure your CAPTCHA images are in the 'captcha_images' directory")
        return False
    
    # Count image files
    extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
    image_count = 0
    
    for ext in extensions:
        image_count += len(list(image_dir.glob(f"*{ext}")))
        image_count += len(list(image_dir.glob(f"*{ext.upper()}")))
    
    if image_count == 0:
        print("❌ No image files found in captcha_images directory")
        return False
    
    print(f"✅ Found {image_count} images in captcha_images directory")
    return True

def main():
    print("🚀 Setting up CAPTCHA Labeling Tools")
    print("=" * 50)
    
    success = True
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        success = False
    else:
        print(f"✅ Python {sys.version.split()[0]} detected")
    
    # Install Python dependencies
    if not install_python_dependencies():
        success = False
    
    # Check/install Tesseract
    if not check_tesseract():
        print("⚠️  Tesseract OCR not found. Attempting to install...")
        if not install_tesseract():
            print("⚠️  OCR features will not work without Tesseract")
            print("   You can still use manual labeling")
    
    # Create directories
    if not create_directories():
        success = False
    
    # Check image directory
    if not check_image_directory():
        success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 Setup completed successfully!")
        print("\n📖 Quick Start Guide:")
        print("   1. Web Interface:    python captcha_labeler.py --mode web")
        print("   2. Auto-label batch: python captcha_labeler.py --mode auto --count 100")
        print("   3. Command line:     python batch_label.py auto captcha_images")
        print("   4. View statistics:  python captcha_labeler.py --mode stats")
        print("\n🌐 Web interface will be available at: http://localhost:5000")
    else:
        print("❌ Setup completed with errors")
        print("   Please resolve the issues above before proceeding")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
