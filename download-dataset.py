import os
import time
import base64
import threading
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException

def initialize_driver():
    """Initialize Chrome WebDriver with minimal options."""
    options = webdriver.ChromeOptions()
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-extensions")
    options.add_argument("--disable-notifications")
    options.add_argument("--headless")  # Headless mode for speed
    options.add_experimental_option("excludeSwitches", ["enable-logging"])  # Suppress DEPRECATED_ENDPOINT warnings
    try:
        driver = webdriver.Chrome(options=options)
        driver.implicitly_wait(3)
        return driver
    except WebDriverException as e:
        print(f"Error initializing WebDriver: {e}")
        raise

def select_state(driver, state="Bihar"):
    """Select a state to ensure CAPTCHA loads."""
    try:
        WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.NAME, "stateCode"))
        ).click()
        WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((By.XPATH, f"//select[@name='stateCode']/option[text()='{state}']"))
        ).click()
        print(f"Thread: Selected state: {state}")
        time.sleep(1)  # Allow page to stabilize
    except Exception as e:
        print(f"Thread: Failed to select state: {e}")

def download_captcha_batch(start_index, end_index, thread_id):
    """Download a batch of CAPTCHA images in PNG format."""
    os.makedirs('captcha_images', exist_ok=True)
    driver = None
    try:
        driver = initialize_driver()
        driver.get("https://voters.eci.gov.in/download-eroll")
        WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        print(f"Thread {thread_id}: Page loaded successfully")
        
        # Select state to ensure CAPTCHA loads
        select_state(driver)

        for i in range(start_index, end_index):
            for attempt in range(3):  # Increased to 3 retries
                try:
                    # Validate session
                    driver.execute_script("return document.title;")
                    
                    # Locate CAPTCHA image
                    captcha_img = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "img[alt='captcha']"))
                    )
                    img_data = captcha_img.get_attribute("src")
                    if not img_data or 'data:image/jpg;base64,' not in img_data:
                        print(f"Thread {thread_id}: Invalid CAPTCHA format on attempt {attempt+1} for image {i+1}")
                        driver.find_element(By.CSS_SELECTOR, "img[alt='refresh']").click()
                        time.sleep(1)
                        continue

                    # Save image
                    img_data = img_data.split('data:image/jpg;base64,')[1]
                    img_binary = base64.b64decode(img_data)
                    with open(f'captcha_images/captcha_{i+1}.png', 'wb') as f:
                        f.write(img_binary)
                    print(f"Thread {thread_id}: Saved captcha_{i+1}.png")

                    # Refresh CAPTCHA
                    driver.find_element(By.CSS_SELECTOR, "img[alt='refresh']").click()
                    time.sleep(0.5)
                    break

                except (TimeoutException, WebDriverException) as e:
                    print(f"Thread {thread_id}: Error on attempt {attempt+1} for image {i+1}: {e}")
                    if attempt == 2:
                        print(f"Thread {thread_id}: Failed to download CAPTCHA {i+1} after 3 attempts, reloading page")
                        try:
                            driver.get("https://voters.eci.gov.in/download-eroll")
                            WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                            select_state(driver)
                        except:
                            print(f"Thread {thread_id}: Failed to reload page for image {i+1}")
                        break
                    time.sleep(1)
                    try:
                        driver.find_element(By.CSS_SELECTOR, "img[alt='refresh']").click()
                        time.sleep(1)
                    except:
                        pass

    except Exception as e:
        print(f"Thread {thread_id}: Critical error: {e}")
    
    finally:
        if driver:
            try:
                driver.quit()
                print(f"Thread {thread_id}: Browser closed.")
            except Exception as e:
                print(f"Thread {thread_id}: Error closing browser: {e}")

def download_captcha_images(total_images=5000, num_threads=3):
    """Download 5000 CAPTCHA images using multiple threads."""
    images_per_thread = total_images // num_threads
    threads = []

    for i in range(num_threads):
        start_index = i * images_per_thread
        end_index = start_index + images_per_thread if i < num_threads - 1 else total_images
        thread = threading.Thread(target=download_captcha_batch, args=(start_index, end_index, i+1))
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()

    print("Done downloading 5000 CAPTCHA images.")

if __name__ == "__main__":
    download_captcha_images()