# 🔤 CAPTCHA Labeling Tools

A comprehensive toolkit for efficiently labeling thousands of CAPTCHA images for machine learning training.

## 🚀 Quick Start

### 1. Setup
```bash
# Run the setup script to install dependencies
python setup_labeling.py
```

### 2. Start Labeling
```bash
# Launch web interface (recommended)
python captcha_labeler.py --mode web

# Or use command line for batch processing
python batch_label.py auto captcha_images
```

### 3. Access Web Interface
Open your browser and go to: **http://localhost:5000**

## 📁 File Structure

```
your_project/
├── captcha_images/          # Your 3000+ CAPTCHA images
├── labeled_data/            # Output directory
│   ├── train/              # Training images (renamed with labels)
│   ├── val/                # Validation images (renamed with labels)
│   ├── labels.json         # Label mappings
│   └── progress.json       # Progress tracking
├── captcha_labeler.py      # Main labeling tool
├── batch_label.py          # Command-line batch processor
├── templates/labeler.html  # Web interface
└── requirements_labeling.txt
```

## 🎯 Labeling Approaches

### 1. Web Interface (Recommended)
- **Interactive GUI** with image preview
- **OCR suggestions** to speed up labeling
- **Keyboard shortcuts** for efficiency
- **Progress tracking** and statistics
- **Batch operations** for bulk processing

**Features:**
- ✅ Real-time OCR predictions
- ✅ Keyboard shortcuts (Enter, Tab, Esc)
- ✅ Progress visualization
- ✅ Auto-save functionality
- ✅ Batch auto-labeling
- ✅ Data organization

### 2. OCR Pre-labeling
Automatically suggest labels using OCR, then manually review:

```bash
# Auto-label 500 images
python captcha_labeler.py --mode auto --count 500

# Review and correct predictions
python batch_label.py review auto_labels.json
```

### 3. Command Line Batch Processing
For power users who prefer command-line tools:

```bash
# Auto-label all images
python batch_label.py auto captcha_images

# Review predictions interactively
python batch_label.py review auto_labels.json

# Organize dataset
python batch_label.py organize reviewed_labels.json captcha_images
```

## ⌨️ Keyboard Shortcuts (Web Interface)

| Key | Action |
|-----|--------|
| `Enter` | Submit current label |
| `Tab` | Use OCR suggestion |
| `Esc` | Skip current image |
| `Ctrl+R` | Refresh statistics |

## 🔧 Configuration Options

### Web Interface Options
```bash
python captcha_labeler.py --help

Options:
  --mode {web,auto,organize,stats}  Operation mode
  --image-dir DIR                   Input directory (default: captcha_images)
  --output-dir DIR                  Output directory (default: labeled_data)
  --count N                         Number of images for auto-labeling
  --port PORT                       Web interface port (default: 5000)
```

### Batch Processing Options
```bash
python batch_label.py --help

Commands:
  auto <image_dir> [start_idx] [count]    Auto-label images
  review <predictions_file>               Review OCR predictions
  organize <labels_file> <image_dir>      Organize labeled dataset
  stats <image_dir>                       Show statistics
```

## 📊 Workflow Recommendations

### For 3000+ Images - Hybrid Approach

1. **Start with OCR Pre-labeling** (saves 70-80% of time):
   ```bash
   python captcha_labeler.py --mode auto --count 1000
   ```

2. **Review OCR predictions** using web interface:
   ```bash
   python captcha_labeler.py --mode web
   ```

3. **Manual labeling** for remaining images via web interface

4. **Organize dataset** when complete:
   ```bash
   python captcha_labeler.py --mode organize
   ```

### Time Estimates
- **Manual only**: ~3-5 seconds per image = 2.5-4 hours total
- **OCR + Review**: ~1-2 seconds per image = 50-100 minutes total
- **Web interface**: Fastest manual labeling experience

## 🎯 Data Format

Your training script expects filenames to be the labels. The tools will:

1. **Store labels** in `labels.json` during labeling
2. **Rename files** when organizing: `abc123_captcha_1.png`
3. **Split data** into train/val directories (80/20 by default)

### Expected Output Structure
```
labeled_data/
├── train/
│   ├── abc123_captcha_1.png
│   ├── def456_captcha_2.png
│   └── ...
└── val/
    ├── ghi789_captcha_100.png
    ├── jkl012_captcha_101.png
    └── ...
```

## 🔍 OCR Configuration

The tools use multiple OCR approaches for better accuracy:

- **Preprocessing**: Grayscale, threshold, morphological operations
- **Multiple PSM modes**: 6, 7, 8, 13 (different text layouts)
- **Character whitelist**: `abcdefghijklmnopqrstuvwxyz0123456789`
- **Confidence scoring**: Based on consistency across approaches

## 🐛 Troubleshooting

### Tesseract OCR Issues
```bash
# Ubuntu/Debian
sudo apt-get install tesseract-ocr

# macOS
brew install tesseract

# Windows: Download from GitHub releases
```

### Web Interface Not Loading
- Check if port 5000 is available
- Try different port: `--port 8080`
- Check firewall settings

### OCR Not Working
- Verify Tesseract installation: `tesseract --version`
- Check image quality and format
- Try manual labeling if OCR fails

### Performance Issues
- Process images in smaller batches
- Use SSD storage for better I/O
- Close other applications to free RAM

## 📈 Progress Tracking

The tools automatically track:
- **Total images**: Count of all images
- **Labeled count**: Successfully labeled images
- **Progress percentage**: Completion status
- **Session statistics**: Labels per session

## 🔄 Data Validation

Built-in validation ensures:
- ✅ Labels contain only alphanumeric characters
- ✅ Labels are reasonable length (2-8 characters)
- ✅ No duplicate or empty labels
- ✅ Proper file organization

## 💡 Tips for Efficient Labeling

1. **Use OCR first** - Even 60% accuracy saves significant time
2. **Keyboard shortcuts** - Much faster than mouse clicks
3. **Batch processing** - Label similar images together
4. **Take breaks** - Avoid fatigue-induced errors
5. **Double-check** - Review a sample of labels before training

## 🎯 Integration with Your Training

After labeling, your existing `train.py` will work with the organized data:

```python
# Your existing code will work with:
train_ds = CaptchaDataset("labeled_data/train", transform)
val_ds = CaptchaDataset("labeled_data/val", transform)
```

The dataset class extracts labels from filenames, so the organized structure is compatible.

## 📞 Support

If you encounter issues:
1. Check this README for solutions
2. Verify all dependencies are installed
3. Test with a small batch first
4. Check file permissions and disk space

---

**Happy Labeling! 🎉**

With these tools, labeling 3000+ CAPTCHA images becomes manageable and efficient. The combination of OCR pre-labeling and the intuitive web interface can reduce labeling time by 70-80% compared to manual-only approaches.
