#!/usr/bin/env python3
"""
Direct Rename CAPTCHA Labeler
Renames images directly with their labels as you type them
Much simpler approach - no JSON files needed!
"""

import os
import shutil
import random
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_from_directory
import argparse
from typing import List, Tuple

class DirectRenameLabeler:
    def __init__(self, image_dir: str = "captcha_images", 
                 labeled_dir: str = "labeled_images",
                 unlabeled_dir: str = "unlabeled_images"):
        self.image_dir = Path(image_dir)
        self.labeled_dir = Path(labeled_dir)
        self.unlabeled_dir = Path(unlabeled_dir)
        
        # Create directories
        self.labeled_dir.mkdir(exist_ok=True)
        self.unlabeled_dir.mkdir(exist_ok=True)
        
        # Move all original images to unlabeled directory if first run
        self.initialize_unlabeled_images()
        
    def initialize_unlabeled_images(self):
        """Move original images to unlabeled directory if not done already"""
        if not list(self.unlabeled_dir.glob("*")):
            print("🔄 Moving original images to unlabeled directory...")
            extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
            moved_count = 0
            
            for ext in extensions:
                for img_file in self.image_dir.glob(f"*{ext}"):
                    dst_path = self.unlabeled_dir / img_file.name
                    if not dst_path.exists():
                        shutil.move(str(img_file), str(dst_path))
                        moved_count += 1
                        
                for img_file in self.image_dir.glob(f"*{ext.upper()}"):
                    dst_path = self.unlabeled_dir / img_file.name
                    if not dst_path.exists():
                        shutil.move(str(img_file), str(dst_path))
                        moved_count += 1
            
            if moved_count > 0:
                print(f"✅ Moved {moved_count} images to unlabeled directory")
    
    def get_unlabeled_images(self) -> List[str]:
        """Get list of unlabeled images"""
        extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        files = []
        
        for ext in extensions:
            files.extend(self.unlabeled_dir.glob(f"*{ext}"))
            files.extend(self.unlabeled_dir.glob(f"*{ext.upper()}"))
            
        return [f.name for f in sorted(files)]
    
    def get_labeled_images(self) -> List[str]:
        """Get list of already labeled images"""
        extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
        files = []
        
        for ext in extensions:
            files.extend(self.labeled_dir.glob(f"*{ext}"))
            files.extend(self.labeled_dir.glob(f"*{ext.upper()}"))
            
        return [f.name for f in sorted(files)]
    
    def get_next_unlabeled_image(self) -> Tuple[str, int]:
        """Get the next unlabeled image"""
        unlabeled_images = self.get_unlabeled_images()
        if unlabeled_images:
            return unlabeled_images[0], 0
        return None, -1
    
    def label_image(self, image_file: str, label: str) -> bool:
        """Label image by renaming it directly"""
        if not label or not label.strip():
            return False
        
        # Clean the label
        clean_label = ''.join(c for c in label.lower().strip() if c.isalnum())
        
        if not clean_label:
            return False
        
        # Get file extension
        src_path = self.unlabeled_dir / image_file
        if not src_path.exists():
            return False
        
        file_ext = src_path.suffix
        
        # Create new filename with label
        new_filename = f"{clean_label}{file_ext}"
        dst_path = self.labeled_dir / new_filename
        
        # Handle duplicate labels by adding a counter
        counter = 1
        while dst_path.exists():
            new_filename = f"{clean_label}_{counter}{file_ext}"
            dst_path = self.labeled_dir / new_filename
            counter += 1
        
        try:
            # Move file from unlabeled to labeled directory with new name
            shutil.move(str(src_path), str(dst_path))
            return True
        except Exception as e:
            print(f"Error moving file: {e}")
            return False
    
    def skip_image(self, image_file: str) -> bool:
        """Skip an image (move to end of queue)"""
        src_path = self.unlabeled_dir / image_file
        if not src_path.exists():
            return False
        
        # Create a temporary name to move it to the end
        temp_name = f"zzz_skipped_{image_file}"
        temp_path = self.unlabeled_dir / temp_name
        
        try:
            shutil.move(str(src_path), str(temp_path))
            return True
        except Exception as e:
            print(f"Error skipping file: {e}")
            return False
    
    def get_stats(self) -> dict:
        """Get labeling statistics"""
        unlabeled_count = len(self.get_unlabeled_images())
        labeled_count = len(self.get_labeled_images())
        total_count = unlabeled_count + labeled_count
        
        progress_percent = (labeled_count / total_count * 100) if total_count > 0 else 0
        
        return {
            "total_images": total_count,
            "labeled_count": labeled_count,
            "unlabeled_count": unlabeled_count,
            "progress_percent": progress_percent
        }
    
    def organize_for_training(self, train_split: float = 0.8):
        """Organize labeled images into train/val directories"""
        labeled_images = self.get_labeled_images()
        
        if not labeled_images:
            return 0, 0
        
        # Create train/val directories
        train_dir = Path("data/train")
        val_dir = Path("data/val")
        
        train_dir.mkdir(parents=True, exist_ok=True)
        val_dir.mkdir(parents=True, exist_ok=True)
        
        # Clear existing files
        for f in train_dir.glob("*"):
            f.unlink()
        for f in val_dir.glob("*"):
            f.unlink()
        
        # Shuffle and split
        random.shuffle(labeled_images)
        split_idx = int(len(labeled_images) * train_split)
        
        train_files = labeled_images[:split_idx]
        val_files = labeled_images[split_idx:]
        
        # Copy files to train/val directories
        for filename in train_files:
            src_path = self.labeled_dir / filename
            dst_path = train_dir / filename
            shutil.copy2(str(src_path), str(dst_path))
        
        for filename in val_files:
            src_path = self.labeled_dir / filename
            dst_path = val_dir / filename
            shutil.copy2(str(src_path), str(dst_path))
        
        return len(train_files), len(val_files)

# Flask Web Interface
app = Flask(__name__)
labeler = None

@app.route('/')
def index():
    return render_template('direct_labeler.html')

@app.route('/api/stats')
def get_stats():
    return jsonify(labeler.get_stats())

@app.route('/api/next_image')
def get_next_image():
    image_file, index = labeler.get_next_unlabeled_image()
    if image_file:
        return jsonify({
            "image_file": image_file,
            "index": index
        })
    return jsonify({"image_file": None})

@app.route('/api/label', methods=['POST'])
def label_image():
    data = request.json
    image_file = data.get('image_file')
    label = data.get('label')
    
    success = labeler.label_image(image_file, label)
    return jsonify({"success": success})

@app.route('/api/skip', methods=['POST'])
def skip_image():
    data = request.json
    image_file = data.get('image_file')
    
    success = labeler.skip_image(image_file)
    return jsonify({"success": success})

@app.route('/api/organize')
def organize_data():
    train_count, val_count = labeler.organize_for_training()
    return jsonify({"train_count": train_count, "val_count": val_count})

@app.route('/images/<filename>')
def serve_image(filename):
    return send_from_directory(labeler.unlabeled_dir, filename)

@app.route('/labeled/<filename>')
def serve_labeled_image(filename):
    return send_from_directory(labeler.labeled_dir, filename)

def main():
    global labeler
    
    parser = argparse.ArgumentParser(description='Direct Rename CAPTCHA Labeler')
    parser.add_argument('--mode', choices=['web', 'stats', 'organize'], 
                       default='web', help='Operation mode')
    parser.add_argument('--image-dir', default='captcha_images', 
                       help='Directory containing original CAPTCHA images')
    parser.add_argument('--labeled-dir', default='labeled_images', 
                       help='Directory for labeled images')
    parser.add_argument('--unlabeled-dir', default='unlabeled_images', 
                       help='Directory for unlabeled images')
    parser.add_argument('--port', type=int, default=5001, 
                       help='Port for web interface')
    
    args = parser.parse_args()
    
    labeler = DirectRenameLabeler(args.image_dir, args.labeled_dir, args.unlabeled_dir)
    
    if args.mode == 'web':
        print(f"🌐 Starting Direct Rename Labeler at http://localhost:{args.port}")
        print("📁 Images will be renamed directly with their labels")
        print("Press Ctrl+C to stop")
        app.run(debug=True, port=args.port, host='0.0.0.0')
        
    elif args.mode == 'stats':
        stats = labeler.get_stats()
        print(f"📊 Labeling Statistics:")
        print(f"   Total images: {stats['total_images']}")
        print(f"   Labeled: {stats['labeled_count']}")
        print(f"   Unlabeled: {stats['unlabeled_count']}")
        print(f"   Progress: {stats['progress_percent']:.1f}%")
        
    elif args.mode == 'organize':
        print("📁 Organizing labeled images for training...")
        train_count, val_count = labeler.organize_for_training()
        print(f"✅ Organized: {train_count} train, {val_count} validation images")
        print("   Ready for training in data/train and data/val directories")

if __name__ == '__main__':
    main()
